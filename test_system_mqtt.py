#!/usr/bin/env python3
"""
Test the actual MQTT notification tool from the system.
"""

import sys
import os
import asyncio
from datetime import datetime

# Add the notification module to the path
sys.path.append('/merge/agent_develop')

try:
    from notification.tools.mqtt import MqttTool, MqttInput
    print("✅ Successfully imported MqttTool from system")
except ImportError as e:
    print(f"❌ Failed to import MqttTool: {e}")
    sys.exit(1)

async def test_system_mqtt_tool():
    """Test the actual system MQTT tool."""
    print("\n=== Testing System MQTT Tool ===")
    
    # Create MQTT tool instance
    mqtt_tool = MqttTool()
    
    # Test basic functionality
    print(f"Tool Name: {mqtt_tool.name}")
    print(f"Tool Description: {mqtt_tool.description}")
    
    # Test with alert_message format (Alert Manager style)
    print("\n--- Testing Alert Manager Format ---")
    try:
        alert_input = MqttInput(
            alert_message="Critical shortage detected in inventory system"
        )
        
        result = await mqtt_tool.execute(alert_input)
        print(f"✅ Alert Manager format result: {result}")
        
    except Exception as e:
        print(f"❌ Alert Manager format failed: {e}")
    
    # Test with subject/content format (Legacy style)
    print("\n--- Testing Legacy Format ---")
    try:
        legacy_input = MqttInput(
            subject="System Alert",
            content="This is a test alert from the system MQTT tool"
        )
        
        result = await mqtt_tool.execute(legacy_input)
        print(f"✅ Legacy format result: {result}")
        
    except Exception as e:
        print(f"❌ Legacy format failed: {e}")
    
    # Test timeout scenarios
    print("\n--- Testing Timeout Handling ---")
    try:
        # This should work normally
        timeout_input = MqttInput(
            subject="Timeout Test",
            content="Testing timeout handling in system MQTT tool"
        )
        
        start_time = datetime.now()
        result = await mqtt_tool.execute(timeout_input)
        end_time = datetime.now()
        
        execution_time = (end_time - start_time).total_seconds() * 1000
        print(f"✅ Timeout test result: {result}")
        print(f"✅ Execution time: {execution_time:.2f}ms")
        
    except Exception as e:
        print(f"❌ Timeout test failed: {e}")

async def test_mqtt_connectivity_check():
    """Test the MQTT connectivity check function."""
    print("\n=== Testing MQTT Connectivity Check ===")
    
    mqtt_tool = MqttTool()
    
    # Test the private connectivity check method
    try:
        is_reachable = mqtt_tool._check_broker_connectivity()
        if is_reachable:
            print("✅ MQTT broker connectivity check passed")
        else:
            print("❌ MQTT broker connectivity check failed")
        
        return is_reachable
        
    except Exception as e:
        print(f"❌ Connectivity check error: {e}")
        return False

async def test_multiple_rapid_messages():
    """Test sending multiple rapid MQTT messages."""
    print("\n=== Testing Multiple Rapid Messages ===")
    
    mqtt_tool = MqttTool()
    
    successful = 0
    failed = 0
    
    for i in range(10):
        try:
            rapid_input = MqttInput(
                subject=f"Rapid Test {i+1}",
                content=f"This is rapid message #{i+1} to test system performance"
            )
            
            result = await mqtt_tool.execute(rapid_input)
            if "successfully" in result:
                successful += 1
                print(f"✅ Message {i+1}: Success")
            else:
                failed += 1
                print(f"❌ Message {i+1}: Unexpected result - {result}")
                
        except Exception as e:
            failed += 1
            print(f"❌ Message {i+1}: Failed - {e}")
    
    print(f"\n📊 Rapid Messages Results: {successful} successful, {failed} failed")
    return failed == 0

async def main():
    """Run all system MQTT tests."""
    print("🚀 Starting System MQTT Tool Tests")
    print("=" * 50)
    
    # Test connectivity first
    connectivity_ok = await test_mqtt_connectivity_check()
    
    if not connectivity_ok:
        print("⚠️  MQTT broker not reachable. Some tests may fail.")
    
    # Run all tests
    tests = [
        ("System MQTT Tool", test_system_mqtt_tool),
        ("Multiple Rapid Messages", test_multiple_rapid_messages)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        try:
            result = await test_func()
            # If test_func doesn't return a boolean, assume success if no exception
            if result is None:
                result = True
            results[test_name] = result
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 SYSTEM MQTT TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All system MQTT tests passed!")
    else:
        print("⚠️  Some system MQTT tests failed - check logs above")

if __name__ == "__main__":
    asyncio.run(main())
