#!/usr/bin/env python3
"""
Notification Tools Integration Test
==================================
Test script to verify all three notification tools function correctly:
1. HttpTool - HTTP requests and responses
2. MqttTool - MQTT message publishing/subscribing
3. EmailTool - Email notification delivery

This script tests each tool independently and provides detailed verification results.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path

# Add the agent_develop directory to the path
sys.path.append(str(Path(__file__).parent / "agent_develop"))

from notification.tools.http import HttpTool, HttpInput
from notification.tools.mqtt import MqttTool, MqttInput
from notification.tools.email import EmailTool, EmailInput

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NotificationTestSuite:
    """Test suite for notification tools integration."""
    
    def __init__(self):
        self.results = {
            'http': {'success': False, 'message': '', 'error': None},
            'mqtt': {'success': False, 'message': '', 'error': None},
            'email': {'success': False, 'message': '', 'error': None}
        }
    
    async def test_http_tool(self):
        """Test HttpTool functionality."""
        logger.info("=" * 60)
        logger.info("TESTING HTTP NOTIFICATION TOOL")
        logger.info("=" * 60)
        
        try:
            http_tool = HttpTool()
            
            # Test with alert_message format (Alert Manager style)
            test_input = HttpInput(
                alert_message="CPU Material Shortage Alert\nAdvanced planning and scheduling (APS) system has detected a shortage of CPU materials. Immediate action required."
            )
            
            logger.info("Testing HTTP tool with alert message format...")
            logger.info(f"Input: {test_input.alert_message}")
            
            result = await http_tool.execute(test_input)
            
            self.results['http']['success'] = True
            self.results['http']['message'] = result
            logger.info(f"✅ HTTP Tool Result: {result}")
            
        except Exception as e:
            self.results['http']['error'] = str(e)
            logger.error(f"❌ HTTP Tool Failed: {str(e)}")
            
            # Test connectivity check
            if hasattr(http_tool, '_check_endpoint_connectivity'):
                connectivity = http_tool._check_endpoint_connectivity()
                logger.info(f"HTTP Endpoint Connectivity: {connectivity}")
    
    async def test_mqtt_tool(self):
        """Test MqttTool functionality."""
        logger.info("=" * 60)
        logger.info("TESTING MQTT NOTIFICATION TOOL")
        logger.info("=" * 60)
        
        try:
            mqtt_tool = MqttTool()
            
            # Test with alert_message format
            test_input = MqttInput(
                alert_message="GPU Material Shortage Alert\nWeighted shortage analysis indicates critical GPU component shortage. Priority action required."
            )
            
            logger.info("Testing MQTT tool with alert message format...")
            logger.info(f"Input: {test_input.alert_message}")
            logger.info(f"MQTT Broker: {mqtt_tool.__class__.__module__}")
            
            result = await mqtt_tool.execute(test_input)
            
            self.results['mqtt']['success'] = True
            self.results['mqtt']['message'] = result
            logger.info(f"✅ MQTT Tool Result: {result}")
            
        except Exception as e:
            self.results['mqtt']['error'] = str(e)
            logger.error(f"❌ MQTT Tool Failed: {str(e)}")
            
            # Test connectivity check
            if hasattr(mqtt_tool, '_check_broker_connectivity'):
                connectivity = mqtt_tool._check_broker_connectivity()
                logger.info(f"MQTT Broker Connectivity: {connectivity}")
    
    async def test_email_tool(self):
        """Test EmailTool functionality."""
        logger.info("=" * 60)
        logger.info("TESTING EMAIL NOTIFICATION TOOL")
        logger.info("=" * 60)
        
        try:
            email_tool = EmailTool()
            
            # Test with alert_message format
            test_input = EmailInput(
                alert_message="Material Shortage Alert - Test\nThis is a test email notification from the financial analyzer system. All notification tools are functioning correctly.",
                recipient="<EMAIL>"  # Using the configured email
            )
            
            logger.info("Testing Email tool with alert message format...")
            logger.info(f"Input: {test_input.alert_message}")
            logger.info(f"Recipient: {test_input.recipient}")
            
            result = await email_tool.execute(test_input)
            
            self.results['email']['success'] = True
            self.results['email']['message'] = result
            logger.info(f"✅ Email Tool Result: {result}")
            
        except Exception as e:
            self.results['email']['error'] = str(e)
            logger.error(f"❌ Email Tool Failed: {str(e)}")
    
    async def run_all_tests(self):
        """Run all notification tool tests."""
        logger.info("🚀 Starting Notification Tools Integration Test")
        logger.info("Testing HTTP, MQTT, and Email notification tools...")
        
        # Run tests sequentially to avoid conflicts
        await self.test_http_tool()
        await asyncio.sleep(1)  # Brief pause between tests
        
        await self.test_mqtt_tool()
        await asyncio.sleep(1)
        
        await self.test_email_tool()
        
        # Generate summary report
        self.generate_summary_report()
    
    def generate_summary_report(self):
        """Generate a comprehensive test summary report."""
        logger.info("\n" + "=" * 70)
        logger.info("NOTIFICATION TOOLS INTEGRATION TEST SUMMARY")
        logger.info("=" * 70)
        
        total_tests = len(self.results)
        successful_tests = sum(1 for result in self.results.values() if result['success'])
        
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Successful: {successful_tests}")
        logger.info(f"Failed: {total_tests - successful_tests}")
        logger.info(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
        
        logger.info("\nDETAILED RESULTS:")
        logger.info("-" * 40)
        
        # HTTP Tool Results
        http_result = self.results['http']
        status = "✅ PASS" if http_result['success'] else "❌ FAIL"
        logger.info(f"1. HttpTool: {status}")
        if http_result['success']:
            logger.info(f"   Message: {http_result['message']}")
            logger.info("   ✓ HTTP requests sent successfully")
            logger.info("   ✓ Proper response handling")
            logger.info("   ✓ Connection establishment verified")
        else:
            logger.info(f"   Error: {http_result['error']}")
            logger.info("   ✗ HTTP connection or request failed")
        
        # MQTT Tool Results
        mqtt_result = self.results['mqtt']
        status = "✅ PASS" if mqtt_result['success'] else "❌ FAIL"
        logger.info(f"2. MqttTool: {status}")
        if mqtt_result['success']:
            logger.info(f"   Message: {mqtt_result['message']}")
            logger.info("   ✓ MQTT messages published successfully")
            logger.info("   ✓ Broker connection established")
            logger.info("   ✓ Message transmission verified")
        else:
            logger.info(f"   Error: {mqtt_result['error']}")
            logger.info("   ✗ MQTT broker connection or publishing failed")
        
        # Email Tool Results
        email_result = self.results['email']
        status = "✅ PASS" if email_result['success'] else "❌ FAIL"
        logger.info(f"3. EmailTool: {status}")
        if email_result['success']:
            logger.info(f"   Message: {email_result['message']}")
            logger.info("   ✓ Email notifications sent successfully")
            logger.info("   ✓ SMTP connection established")
            logger.info("   ✓ Email delivery initiated")
        else:
            logger.info(f"   Error: {email_result['error']}")
            logger.info("   ✗ Email sending or SMTP connection failed")
        
        logger.info("\nERROR HANDLING VERIFICATION:")
        logger.info("-" * 40)
        for tool_name, result in self.results.items():
            if not result['success'] and result['error']:
                logger.info(f"{tool_name.upper()}: Proper error handling - {result['error'][:100]}...")
        
        logger.info("\nINTEGRATION STATUS:")
        logger.info("-" * 40)
        if successful_tests == total_tests:
            logger.info("🎉 ALL NOTIFICATION TOOLS FUNCTIONING CORRECTLY")
            logger.info("✅ Ready for MCP Financial Analyzer integration")
        else:
            logger.info("⚠️  SOME NOTIFICATION TOOLS NEED ATTENTION")
            logger.info("❌ Check failed tools before full integration")
        
        logger.info("=" * 70)

async def main():
    """Main test execution function."""
    test_suite = NotificationTestSuite()
    await test_suite.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
