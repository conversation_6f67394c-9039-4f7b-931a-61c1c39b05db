#!/usr/bin/env python3
"""
Test script for MQTT alert verification.
Tests MQTT connection timeouts, publish timeouts, and broker connectivity.
"""

import paho.mqtt.client as mqtt
import socket
import time
import json
import threading
import queue
from datetime import datetime

# MQTT configuration from the system
BROKER_ADDRESS = "************"  # Original broker
LOCAL_BROKER = "localhost"       # Fallback local broker
PORT = 1883
TOPIC = "/notification"

# Timeout configurations (matching system config)
CONNECTION_TIMEOUT = 5.0  # seconds
PUBLISH_TIMEOUT = 10.0    # seconds
SOCKET_TIMEOUT = 3.0      # seconds

def check_broker_connectivity(broker_host, port):
    """Check if MQTT broker is reachable with a quick socket test."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(SOCKET_TIMEOUT)
        result = sock.connect_ex((broker_host, port))
        sock.close()
        return result == 0
    except Exception:
        return False

def test_broker_connectivity():
    """Test connectivity to both original and local MQTT brokers."""
    print(f"\n=== Testing MQTT Broker Connectivity ===")
    
    # Test original broker
    print(f"Testing original broker: {BROKER_ADDRESS}:{PORT}")
    original_reachable = check_broker_connectivity(BROKER_ADDRESS, PORT)
    if original_reachable:
        print(f"✅ Original broker {BROKER_ADDRESS}:{PORT} is reachable")
    else:
        print(f"❌ Original broker {BROKER_ADDRESS}:{PORT} is not reachable")
    
    # Test local broker
    print(f"Testing local broker: {LOCAL_BROKER}:{PORT}")
    local_reachable = check_broker_connectivity(LOCAL_BROKER, PORT)
    if local_reachable:
        print(f"✅ Local broker {LOCAL_BROKER}:{PORT} is reachable")
    else:
        print(f"❌ Local broker {LOCAL_BROKER}:{PORT} is not reachable")
    
    # Determine which broker to use
    if original_reachable:
        return BROKER_ADDRESS, True
    elif local_reachable:
        return LOCAL_BROKER, True
    else:
        return None, False

def create_mqtt_client():
    """Create MQTT client compatible with different paho-mqtt versions."""
    try:
        client = mqtt.Client()
        return client
    except TypeError:
        # Handle newer paho-mqtt versions that require callback_api_version
        import paho.mqtt.client as mqtt_client
        return mqtt_client.Client(callback_api_version=mqtt_client.CallbackAPIVersion.VERSION1)

def test_basic_mqtt_publish(broker_host):
    """Test basic MQTT publish functionality."""
    print(f"\n=== Testing Basic MQTT Publish ===")
    print(f"Target Broker: {broker_host}:{PORT}")
    print(f"Topic: {TOPIC}")
    print(f"Timeouts: Connection={CONNECTION_TIMEOUT}s, Publish={PUBLISH_TIMEOUT}s")
    
    try:
        client = create_mqtt_client()
        
        # Connection with timeout
        start_time = time.time()
        connection_result = client.connect(broker_host, PORT, keepalive=int(CONNECTION_TIMEOUT))
        if connection_result != 0:
            raise ConnectionError(f"MQTT connection failed with code: {connection_result}")
        
        # Prepare and publish message
        test_message = {
            "subject": "Test MQTT Alert",
            "content": "This is a test MQTT alert message",
            "timestamp": datetime.now().isoformat(),
            "severity": "INFO"
        }
        
        msg_info = client.publish(TOPIC, json.dumps(test_message))
        end_time = time.time()
        
        # Disconnect
        client.disconnect()
        
        response_time = (end_time - start_time) * 1000
        print(f"✅ MQTT message published successfully")
        print(f"✅ Message ID: {msg_info.mid}")
        print(f"✅ Response Time: {response_time:.2f}ms")
        
        return True
        
    except socket.timeout:
        print(f"❌ MQTT connection timeout to {broker_host}:{PORT}")
        return False
    except socket.gaierror as e:
        print(f"❌ MQTT broker DNS resolution failed: {str(e)}")
        return False
    except ConnectionRefusedError:
        print(f"❌ MQTT connection refused by {broker_host}:{PORT}")
        return False
    except Exception as e:
        print(f"❌ MQTT operation failed: {str(e)}")
        return False

def test_mqtt_timeout_scenarios(broker_host):
    """Test various MQTT timeout scenarios."""
    print(f"\n=== Testing MQTT Timeout Scenarios ===")
    
    # Test with very short timeout
    print("\n--- Testing Short Connection Timeout ---")
    try:
        client = create_mqtt_client()
        # Simulate timeout by using a very short keepalive
        connection_result = client.connect(broker_host, PORT, keepalive=1)
        if connection_result == 0:
            print("✅ Short timeout connection established")
            client.disconnect()
        else:
            print(f"❌ Short timeout connection failed: {connection_result}")
    except Exception as e:
        print(f"❌ Short timeout test failed: {e}")
    
    # Test connection timeout with unreachable host
    print("\n--- Testing Connection Timeout with Unreachable Host ---")
    try:
        client = create_mqtt_client()
        start_time = time.time()
        # Try to connect to a non-existent host
        connection_result = client.connect("*********", PORT, keepalive=int(CONNECTION_TIMEOUT))
        end_time = time.time()
        
        if connection_result != 0:
            print(f"✅ Connection timeout handled correctly (took {(end_time - start_time):.2f}s)")
        else:
            print("❌ Unexpected successful connection to unreachable host")
            client.disconnect()
    except Exception as e:
        print(f"✅ Connection timeout exception handled: {e}")

def test_mqtt_large_payload(broker_host):
    """Test MQTT with large payload."""
    print(f"\n=== Testing MQTT Large Payload ===")
    
    try:
        client = create_mqtt_client()
        
        connection_result = client.connect(broker_host, PORT, keepalive=int(CONNECTION_TIMEOUT))
        if connection_result != 0:
            raise ConnectionError(f"MQTT connection failed with code: {connection_result}")
        
        # Create large payload (but within MQTT limits)
        large_content = "A" * 5000  # 5KB payload
        large_message = {
            "subject": "Large MQTT Payload Test",
            "content": large_content,
            "timestamp": datetime.now().isoformat()
        }
        
        start_time = time.time()
        msg_info = client.publish(TOPIC, json.dumps(large_message))
        end_time = time.time()
        
        client.disconnect()
        
        response_time = (end_time - start_time) * 1000
        payload_size = len(json.dumps(large_message))
        
        print(f"✅ Large MQTT payload sent successfully")
        print(f"✅ Message ID: {msg_info.mid}")
        print(f"✅ Payload size: {payload_size} bytes")
        print(f"✅ Response Time: {response_time:.2f}ms")
        
        return True
        
    except Exception as e:
        print(f"❌ Large payload test failed: {e}")
        return False

def test_mqtt_concurrent_publishes(broker_host):
    """Test multiple concurrent MQTT publishes."""
    print(f"\n=== Testing Concurrent MQTT Publishes ===")
    
    results = queue.Queue()
    
    def publish_message(client_id):
        try:
            client = create_mqtt_client()
            connection_result = client.connect(broker_host, PORT, keepalive=int(CONNECTION_TIMEOUT))
            if connection_result != 0:
                raise ConnectionError(f"Connection failed: {connection_result}")
            
            message = {
                "subject": f"Concurrent MQTT Test {client_id}",
                "content": f"This is concurrent MQTT message #{client_id}",
                "client_id": client_id,
                "timestamp": datetime.now().isoformat()
            }
            
            msg_info = client.publish(TOPIC, json.dumps(message))
            client.disconnect()
            
            results.put((client_id, True, msg_info.mid))
            
        except Exception as e:
            results.put((client_id, False, str(e)))
    
    # Launch 5 concurrent publishers
    threads = []
    for i in range(5):
        thread = threading.Thread(target=publish_message, args=(i+1,))
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    # Collect results
    successful = 0
    failed = 0
    while not results.empty():
        client_id, success, result = results.get()
        if success:
            print(f"✅ Client {client_id}: Success (Message ID: {result})")
            successful += 1
        else:
            print(f"❌ Client {client_id}: Failed ({result})")
            failed += 1
    
    print(f"\n📊 Concurrent Test Results: {successful} successful, {failed} failed")
    return failed == 0

def setup_local_mqtt_broker():
    """Attempt to set up a local MQTT broker for testing."""
    print(f"\n=== Setting Up Local MQTT Broker ===")
    
    try:
        # Try to install and start mosquitto
        import subprocess
        
        print("Attempting to install mosquitto MQTT broker...")
        
        # Check if mosquitto is already installed
        result = subprocess.run(['which', 'mosquitto'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Mosquitto is already installed")
        else:
            print("Installing mosquitto...")
            # Try different package managers
            for cmd in [
                ['apt-get', 'update', '&&', 'apt-get', 'install', '-y', 'mosquitto'],
                ['yum', 'install', '-y', 'mosquitto'],
                ['brew', 'install', 'mosquitto']
            ]:
                try:
                    subprocess.run(cmd, check=True, capture_output=True)
                    print("✅ Mosquitto installed successfully")
                    break
                except:
                    continue
            else:
                print("❌ Could not install mosquitto automatically")
                return False
        
        # Start mosquitto
        print("Starting mosquitto broker...")
        subprocess.Popen(['mosquitto', '-p', str(PORT)], 
                        stdout=subprocess.DEVNULL, 
                        stderr=subprocess.DEVNULL)
        
        # Wait a moment for broker to start
        time.sleep(2)
        
        # Test if broker is running
        if check_broker_connectivity(LOCAL_BROKER, PORT):
            print(f"✅ Local MQTT broker started successfully on {LOCAL_BROKER}:{PORT}")
            return True
        else:
            print("❌ Failed to start local MQTT broker")
            return False
            
    except Exception as e:
        print(f"❌ Error setting up local MQTT broker: {e}")
        return False

def main():
    """Run all MQTT alert tests."""
    print("🚀 Starting MQTT Alert Verification Tests")
    print("=" * 50)
    
    # First, test broker connectivity
    broker_host, broker_available = test_broker_connectivity()
    
    if not broker_available:
        print("\n⚠️  No MQTT broker available. Attempting to set up local broker...")
        if setup_local_mqtt_broker():
            broker_host = LOCAL_BROKER
            broker_available = True
        else:
            print("❌ Could not set up any MQTT broker. Skipping MQTT tests.")
            return
    
    print(f"\n🎯 Using MQTT broker: {broker_host}:{PORT}")
    
    # Run tests
    tests = [
        ("Basic MQTT Publish", lambda: test_basic_mqtt_publish(broker_host)),
        ("MQTT Timeout Scenarios", lambda: test_mqtt_timeout_scenarios(broker_host)),
        ("MQTT Large Payload", lambda: test_mqtt_large_payload(broker_host)),
        ("Concurrent MQTT Publishes", lambda: test_mqtt_concurrent_publishes(broker_host))
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 MQTT TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    print(f"Broker used: {broker_host}:{PORT}")
    
    if passed == total:
        print("🎉 All MQTT alert tests passed!")
    else:
        print("⚠️  Some MQTT alert tests failed - check logs above")

if __name__ == "__main__":
    main()
