#!/usr/bin/env python3
"""
Test script to validate the refactored AlertManagerAgent.
Verifies that the agent can be instantiated and the structured prompt is generated correctly.
"""

import sys
import os

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_alert_manager_instantiation():
    """Test that AlertManagerAgent can be instantiated with the refactored prompt structure."""
    try:
        from agents.alert_manager_agent import AlertManagerAgent, create_alert_manager_agent
        
        print("✓ Successfully imported AlertManagerAgent")
        
        # Test the static instruction builder
        test_config = {
            'shortage_threshold': 0.8,
            'enabled_channels': ['email', 'mqtt'],
            'revenue_decline_threshold': 0.12,
            'profit_margin_threshold': 0.18
        }
        
        instruction = AlertManagerAgent._build_instruction_static("Test Company", test_config)
        
        print("✓ Successfully generated structured instruction")
        print(f"✓ Instruction length: {len(instruction)} characters")
        
        # Verify instruction contains expected sections
        expected_sections = [
            "AlertManagerAgent",
            "PROCESSING STEPS:",
            "OUTPUT REQUIREMENTS:",
            "ALERT CONFIGURATION FOR TEST COMPANY:",
            "ALERT SEVERITY CRITERIA:",
            "NOTIFICATION CHANNEL SELECTION:"
        ]
        
        missing_sections = []
        for section in expected_sections:
            if section not in instruction:
                missing_sections.append(section)
        
        if missing_sections:
            print(f"✗ Missing sections: {missing_sections}")
            return False
        else:
            print("✓ All expected sections present in instruction")
        
        # Test configuration values are properly included
        if "0.8" in instruction and "email, mqtt" in instruction:
            print("✓ Configuration values properly included")
        else:
            print("✗ Configuration values not properly included")
            return False
        
        # Test factory function
        try:
            # Note: This may fail if atomic-agents is not available, but should not crash
            agent = create_alert_manager_agent("Test Company", test_config)
            print("✓ Factory function executed successfully")
        except Exception as e:
            print(f"⚠ Factory function failed (expected if dependencies missing): {e}")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_schema_imports():
    """Test that schemas can be imported correctly."""
    try:
        from schemas.agent_schemas import AlertManagementInputSchema, AlertManagementOutputSchema
        print("✓ Successfully imported alert management schemas")
        
        # Test schema instantiation
        input_schema = AlertManagementInputSchema(
            company_name="Test Company",
            alert_message="Test alert",
            channels=["email"],
            severity="HIGH"
        )
        print("✓ Successfully created AlertManagementInputSchema instance")
        
        output_schema = AlertManagementOutputSchema(
            company_name="Test Company",
            alert_summary="Test summary",
            response="Test response"
        )
        print("✓ Successfully created AlertManagementOutputSchema instance")
        
        return True
        
    except ImportError as e:
        print(f"✗ Schema import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Schema instantiation error: {e}")
        return False

def main():
    """Run all validation tests."""
    print("=" * 60)
    print("ALERT MANAGER AGENT REFACTORING VALIDATION")
    print("=" * 60)
    
    tests = [
        ("Agent Instantiation", test_alert_manager_instantiation),
        ("Schema Imports", test_schema_imports)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 60)
    print("VALIDATION SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All validation tests passed!")
        print("The AlertManagerAgent refactoring is successful.")
    else:
        print("\n❌ Some validation tests failed.")
        print("Please review the errors above.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
