#!/usr/bin/env python3
"""
Test script for HTTP alert verification.
Tests connection timeouts, response timeouts, and message delivery.
"""

import requests
import time
import json
from datetime import datetime

# HTTP server configuration
HTTP_URL = "http://localhost:5000/alert"
CONNECTION_TIMEOUT = 5.0  # seconds
READ_TIMEOUT = 10.0       # seconds

def test_basic_alert():
    """Test basic HTTP alert functionality."""
    print(f"\n=== Testing Basic HTTP Alert ===")
    print(f"Target URL: {HTTP_URL}")
    print(f"Timeouts: Connection={CONNECTION_TIMEOUT}s, Read={READ_TIMEOUT}s")
    
    test_data = {
        "subject": "Test Alert",
        "body": "This is a test alert message",
        "timestamp": datetime.now().isoformat(),
        "severity": "INFO"
    }
    
    try:
        start_time = time.time()
        response = requests.post(
            HTTP_URL,
            json=test_data,
            timeout=(CONNECTION_TIMEOUT, READ_TIMEOUT)
        )
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        print(f"✅ Response Status: {response.status_code}")
        print(f"✅ Response Text: {response.text}")
        print(f"✅ Response Time: {response_time:.2f}ms")
        print(f"✅ Request successful!")
        
        return True
        
    except requests.exceptions.ConnectTimeout:
        print(f"❌ Connection timeout after {CONNECTION_TIMEOUT}s")
        return False
    except requests.exceptions.ReadTimeout:
        print(f"❌ Read timeout after {READ_TIMEOUT}s")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_timeout_scenarios():
    """Test various timeout scenarios."""
    print(f"\n=== Testing Timeout Scenarios ===")
    
    # Test with very short timeout
    print("\n--- Testing Short Connection Timeout (0.1s) ---")
    try:
        response = requests.post(
            HTTP_URL,
            json={"test": "short_timeout"},
            timeout=(0.1, READ_TIMEOUT)
        )
        print(f"✅ Short timeout test passed: {response.status_code}")
    except requests.exceptions.ConnectTimeout:
        print("❌ Connection timeout with 0.1s timeout (expected for slow connections)")
    except Exception as e:
        print(f"✅ Short timeout handled: {e}")
    
    # Test with normal timeout
    print("\n--- Testing Normal Timeout ---")
    try:
        response = requests.post(
            HTTP_URL,
            json={"test": "normal_timeout"},
            timeout=(CONNECTION_TIMEOUT, READ_TIMEOUT)
        )
        print(f"✅ Normal timeout test passed: {response.status_code}")
    except Exception as e:
        print(f"❌ Normal timeout failed: {e}")

def test_large_payload():
    """Test with large payload to check read timeout."""
    print(f"\n=== Testing Large Payload ===")
    
    # Create a large payload
    large_content = "A" * 10000  # 10KB of data
    test_data = {
        "subject": "Large Payload Test",
        "body": large_content,
        "timestamp": datetime.now().isoformat()
    }
    
    try:
        start_time = time.time()
        response = requests.post(
            HTTP_URL,
            json=test_data,
            timeout=(CONNECTION_TIMEOUT, READ_TIMEOUT)
        )
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000
        print(f"✅ Large payload sent successfully")
        print(f"✅ Response Status: {response.status_code}")
        print(f"✅ Response Time: {response_time:.2f}ms")
        print(f"✅ Payload size: {len(json.dumps(test_data))} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ Large payload test failed: {e}")
        return False

def test_concurrent_requests():
    """Test multiple concurrent requests."""
    print(f"\n=== Testing Concurrent Requests ===")
    
    import threading
    import queue
    
    results = queue.Queue()
    
    def send_request(request_id):
        try:
            response = requests.post(
                HTTP_URL,
                json={
                    "subject": f"Concurrent Test {request_id}",
                    "body": f"This is concurrent request #{request_id}",
                    "request_id": request_id
                },
                timeout=(CONNECTION_TIMEOUT, READ_TIMEOUT)
            )
            results.put((request_id, True, response.status_code))
        except Exception as e:
            results.put((request_id, False, str(e)))
    
    # Launch 5 concurrent requests
    threads = []
    for i in range(5):
        thread = threading.Thread(target=send_request, args=(i+1,))
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    # Collect results
    successful = 0
    failed = 0
    while not results.empty():
        request_id, success, result = results.get()
        if success:
            print(f"✅ Request {request_id}: Success (Status: {result})")
            successful += 1
        else:
            print(f"❌ Request {request_id}: Failed ({result})")
            failed += 1
    
    print(f"\n📊 Concurrent Test Results: {successful} successful, {failed} failed")
    return failed == 0

def main():
    """Run all HTTP alert tests."""
    print("🚀 Starting HTTP Alert Verification Tests")
    print("=" * 50)
    
    tests = [
        ("Basic Alert", test_basic_alert),
        ("Timeout Scenarios", test_timeout_scenarios),
        ("Large Payload", test_large_payload),
        ("Concurrent Requests", test_concurrent_requests)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All HTTP alert tests passed!")
    else:
        print("⚠️  Some HTTP alert tests failed - check logs above")

if __name__ == "__main__":
    main()
